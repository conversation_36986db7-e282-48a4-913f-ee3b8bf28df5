// Website blocker functionality for CalenTask
let blockedUrls = [];
let tasks = [];
let isBlockingEnabled = true;
let blockingStatus = {
  isBlocking: false,
  currentTask: null,
  blockedAttempts: 0
};

// Function to check if a URL should be blocked
function shouldBlockUrl(url) {
  if (!isBlockingEnabled || blockedUrls.length === 0) {
    return false;
  }

  // Extract domain from URL
  const domain = extractDomain(url);
  
  // Check if the domain is in the blocklist
  return blockedUrls.some(blockedUrl => {
    const blockedDomain = extractDomain(blockedUrl);
    return domain.includes(blockedDomain);
  });
}

// Extract domain from URL
function extractDomain(url) {
  try {
    // Remove protocol and get domain
    let domain = url.replace(/^https?:\/\//, '');
    // Remove path if any
    domain = domain.split('/')[0];
    return domain.toLowerCase();
  } catch (e) {
    return url;
  }
}

// Check if there's an active task scheduled for the current time
function hasActiveScheduledTask() {
  const now = new Date();
  // Normalize both dates to local timezone for comparison
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  console.log(`[Blocker] hasActiveScheduledTask called. Current local time: ${now.toString()}, Today (normalized local): ${today.toString()}`);
  
  // Log a summary of tasks relevant to blocking
  const relevantTasks = tasks.filter(t => t.scheduled && !t.archived);
  const relevantTasksSummary = relevantTasks.map(t => ({
    title: t.title,
    scheduledStart: t.scheduledStart,
    isFullDay: t.isFullDay,
    archived: t.archived,
    // Add more debug info
    startDate: t.scheduledStart ? new Date(t.scheduledStart).toISOString() : 'none',
    hasTime: t.scheduledStart ? t.scheduledStart.includes('T') : false
  }));
  
  console.log(`[Blocker] Relevant scheduled tasks: ${JSON.stringify(relevantTasksSummary, null, 2)}`);

  for (const task of relevantTasks) {
    if (!task.scheduledStart) {
      console.log(`[Blocker] Task "${task.title}" has no scheduledStart, skipping`);
      continue;
    }
    
    try {
      const startTime = new Date(task.scheduledStart);
      
      // Check for full-day task (either isFullDay flag or date string without time component)
      const isFullDayTask = task.isFullDay === true || 
                           (task.scheduledStart && !task.scheduledStart.includes('T'));
      
      console.log(`[Blocker] Checking task: "${task.title}" (isFullDay: ${isFullDayTask})`);
      
      if (isFullDayTask) {
        // For full day tasks, compare just the date parts (year, month, day)
        const taskDate = new Date(task.scheduledStart);
        taskDate.setHours(0, 0, 0, 0);
        
        console.log(`[Blocker]   Task scheduledStart: ${task.scheduledStart}`);
        console.log(`[Blocker]   Parsed task date: ${taskDate.toISOString()} (Local: ${taskDate.toString()})`);
        console.log(`[Blocker]   Today's date: ${today.toISOString()} (Local: ${today.toString()})`);
        
        // Compare date components directly
        const isSameDate = taskDate.getFullYear() === today.getFullYear() &&
                          taskDate.getMonth() === today.getMonth() &&
                          taskDate.getDate() === today.getDate();
                          
        console.log(`[Blocker]   Date comparison result: ${isSameDate}`);

        if (isSameDate) {
          console.log(`[Blocker] Match FOUND for full-day task: "${task.title}". Blocking.`);
          return { isBlocking: true, task };
        }
      } else {
        // Handle time-specific events
        const endTime = task.scheduledEnd ? new Date(task.scheduledEnd) : null;
        if (endTime && now >= startTime && now <= endTime) {
          console.log(`[Blocker] Match FOUND for time-specific task: "${task.title}". Blocking.`);
          return { isBlocking: true, task };
        }
      }
    } catch (error) {
      console.error(`[Blocker] Error processing task "${task.title}":`, error);
    }
  }
  
  console.log('[Blocker] No active blocking task found after checking all tasks.');
  return { isBlocking: false, task: null };
}

// Load data from Chrome storage
function loadData() {
  chrome.storage.local.get(['blockedUrls', 'tasks', 'isBlockingEnabled'], (result) => {
    if (result.blockedUrls) {
      blockedUrls = result.blockedUrls;
    }
    
    if (result.tasks) {
      tasks = result.tasks;
    }
    
    if (result.isBlockingEnabled !== undefined) {
      isBlockingEnabled = result.isBlockingEnabled;
    }
  });
}

// Create a block page for redirecting users
function createBlockPage(url, taskTitle) {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Site Blocked - CalenTask</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background-color: #f5f5f5;
          color: #333;
          line-height: 1.6;
          margin: 0;
          padding: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
        }
        .block-container {
          background: white;
          border-radius: 8px;
          padding: 2rem;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          max-width: 600px;
          text-align: center;
        }
        h1 {
          color: #4e7eff;
          margin-bottom: 1rem;
        }
        .task-info {
          background-color: #f9f9f9;
          padding: 1rem;
          border-radius: 4px;
          margin: 1rem 0;
          border-left: 4px solid #4e7eff;
        }
        .site-url {
          color: #ff4e4e;
          font-weight: bold;
        }
        .buttons {
          margin-top: 1.5rem;
          display: flex;
          justify-content: center;
          gap: 1rem;
        }
        button {
          background-color: #4e7eff;
          color: white;
          border: none;
          padding: 0.6rem 1.2rem;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
          transition: background-color 0.2s;
        }
        button.secondary {
          background-color: #f5f5f5;
          color: #333;
          border: 1px solid #ddd;
        }
        button:hover {
          background-color: #3b5fd9;
        }
        button.secondary:hover {
          background-color: #e5e5e5;
        }
        .timer {
          margin-top: 1rem;
          font-size: 0.9rem;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="block-container">
        <h1>Site Blocked</h1>
        <p>You're trying to access <span class="site-url">${url}</span> during a scheduled task time.</p>
        
        <div class="task-info">
          <h3>Current Task: ${taskTitle}</h3>
          <p>Focus on completing your task before visiting this site.</p>
        </div>
        
        <div class="buttons">
          <button id="back-btn" class="secondary">Go Back</button>
          <button id="open-task-btn">Open CalenTask</button>
        </div>
        
        <div class="timer">
          <p>Blocked attempts: ${blockingStatus.blockedAttempts}</p>
        </div>
      </div>
      
      <script>
        document.getElementById('back-btn').addEventListener('click', () => {
          history.back();
        });
        
        document.getElementById('open-task-btn').addEventListener('click', () => {
          chrome.tabs.create({ url: 'todo.html' });
        });
      </script>
    </body>
    </html>
  `;
}

// Listen for web navigation events
chrome.webNavigation.onBeforeNavigate.addListener((details) => {
  // Only check main frame navigations (not iframes, etc.)
  if (details.frameId !== 0) return;
  
  // Check if blocking is enabled and we have blocked URLs
  if (!isBlockingEnabled || blockedUrls.length === 0) return;
  
  // Check if there's an active scheduled task
  const { isBlocking, task } = hasActiveScheduledTask();
  
  // If there's an active task and the URL should be blocked
  if (isBlocking && task && shouldBlockUrl(details.url)) {
    // Update blocking status
    blockingStatus.isBlocking = true;
    blockingStatus.currentTask = task;
    
    // Cancel the navigation
    chrome.tabs.update(details.tabId, { url: 'about:blank' }, () => {
      // Create a blocking page
      const blockPageUrl = chrome.runtime.getURL('blocked.html') + 
                         `?url=${encodeURIComponent(details.url)}` +
                         `&task=${encodeURIComponent(task.title)}`;
      
      // Navigate to the blocking page
      chrome.tabs.update(details.tabId, { url: blockPageUrl });
      
      // Increment blocked attempts counter
      blockingStatus.blockedAttempts++;
    });
  }
});

// Listen for changes to the storage
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    if (changes.blockedUrls) {
      blockedUrls = changes.blockedUrls.newValue;
    }
    
    if (changes.tasks) {
      tasks = changes.tasks.newValue;
    }
    
    if (changes.isBlockingEnabled) {
      isBlockingEnabled = changes.isBlockingEnabled.newValue;
    }
  }
});

// Initialize data on startup
loadData();

// Notification utilities for CalenTask
// Handles browser notifications for scheduled tasks

/**
 * Notification manager for task notifications
 */
class TaskNotificationManager {
  constructor() {
    this.hasPermission = false;
    this.scheduledNotifications = new Map();
    this.checkPermission();
  }

  /**
   * Check if notification permission is granted
   */
  async checkPermission() {
    if (!('Notification' in window)) {
      console.warn('This browser does not support desktop notifications');
      this.hasPermission = false;
      return false;
    }

    if (Notification.permission === 'granted') {
      this.hasPermission = true;
      return true;
    } else if (Notification.permission !== 'denied') {
      try {
        const permission = await Notification.requestPermission();
        this.hasPermission = permission === 'granted';
        return this.hasPermission;
      } catch (error) {
        console.error('Error requesting notification permission:', error);
        this.hasPermission = false;
        return false;
      }
    }

    return false;
  }

  /**
   * Request notification permission
   * @returns {Promise<boolean>} Whether permission was granted
   */
  async requestPermission() {
    if (!('Notification' in window)) {
      console.warn('This browser does not support desktop notifications');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      this.hasPermission = permission === 'granted';
      return this.hasPermission;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  /**
   * Schedule notifications for a task
   * @param {Object} task The task object
   */
  scheduleTaskNotifications(task) {
    // Only schedule for time-specific tasks (not full-day events)
    if (!task || !task.scheduled || task.isFullDay || !task.scheduledStart || !task.scheduledEnd) {
      return;
    }

    // Clear any existing notifications for this task
    this.clearTaskNotifications(task.id);

    // Schedule start notification
    const startTime = new Date(task.scheduledStart);
    const endTime = new Date(task.scheduledEnd);
    const now = new Date();

    // Calculate duration in minutes
    const durationMs = endTime - startTime;
    const durationMinutes = Math.round(durationMs / 60000);

    // Only schedule if the start time is in the future
    if (startTime > now) {
      const startTimeoutId = setTimeout(() => {
        this.showTaskStartNotification(task, durationMinutes);
      }, startTime - now);

      // Store the timeout ID
      this.scheduledNotifications.set(`${task.id}-start`, startTimeoutId);
    }

    // Only schedule end notification if the end time is in the future
    if (endTime > now) {
      const endTimeoutId = setTimeout(() => {
        this.showTaskEndNotification(task);
      }, endTime - now);

      // Store the timeout ID
      this.scheduledNotifications.set(`${task.id}-end`, endTimeoutId);
    }
  }

  /**
   * Clear scheduled notifications for a task
   * @param {string} taskId The task ID
   */
  clearTaskNotifications(taskId) {
    // Clear start notification
    if (this.scheduledNotifications.has(`${taskId}-start`)) {
      clearTimeout(this.scheduledNotifications.get(`${taskId}-start`));
      this.scheduledNotifications.delete(`${taskId}-start`);
    }

    // Clear end notification
    if (this.scheduledNotifications.has(`${taskId}-end`)) {
      clearTimeout(this.scheduledNotifications.get(`${taskId}-end`));
      this.scheduledNotifications.delete(`${taskId}-end`);
    }
  }

  /**
   * Show notification when a task starts
   * @param {Object} task The task object
   * @param {number} durationMinutes Task duration in minutes
   */
  showTaskStartNotification(task, durationMinutes) {
    if (!this.hasPermission) {
      this.checkPermission();
      return;
    }

    const title = `Task Started: ${task.title}`;
    const options = {
      body: `Duration: ${durationMinutes} minutes`,
      icon: '/images/icon128.png',
      tag: `task-start-${task.id}`,
      requireInteraction: false
    };

    const notification = new Notification(title, options);
    
    notification.onclick = () => {
      // Focus on the extension popup if possible
      if (chrome.runtime && chrome.runtime.getURL) {
        chrome.tabs.create({ url: chrome.runtime.getURL('popup.html') });
      }
      notification.close();
    };
  }

  /**
   * Show notification when a task ends
   * @param {Object} task The task object
   */
  showTaskEndNotification(task) {
    if (!this.hasPermission) {
      this.checkPermission();
      return;
    }

    const title = `Task Ended: ${task.title}`;
    const options = {
      body: 'This scheduled task has concluded.',
      icon: '/images/icon128.png',
      tag: `task-end-${task.id}`,
      requireInteraction: false
    };

    const notification = new Notification(title, options);
    
    notification.onclick = () => {
      // Focus on the extension popup if possible
      if (chrome.runtime && chrome.runtime.getURL) {
        chrome.tabs.create({ url: chrome.runtime.getURL('popup.html') });
      }
      notification.close();
    };
  }

  /**
   * Schedule notifications for all tasks
   * @param {Array} tasks Array of tasks
   * @param {Array} archivedTasks Array of archived tasks
   */
  scheduleAllTaskNotifications(tasks, archivedTasks) {
    // Clear all existing notifications
    this.clearAllNotifications();

    // Schedule notifications for all time-specific tasks
    [...tasks, ...archivedTasks].forEach(task => {
      if (task.scheduled && !task.isFullDay) {
        this.scheduleTaskNotifications(task);
      }
    });
  }

  /**
   * Clear all scheduled notifications
   */
  clearAllNotifications() {
    for (const timeoutId of this.scheduledNotifications.values()) {
      clearTimeout(timeoutId);
    }
    this.scheduledNotifications.clear();
  }
}

// Export to global scope for access from other files
window.TaskNotificationManager = TaskNotificationManager;

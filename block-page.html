<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:;">
  <link rel="icon" type="image/png" href="/images/icon128.png">
  <title>Site Blocked - CalenTask</title>
  <style id="block-page-styles">
    /* Critical styles that must be inlined */
    body, html {
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      background-color: #f8f9fa;
      color: #333;
    }
    
    /* Ensure loading state is visible by default */
    body.loading #loading {
      display: flex !important;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      text-align: center;
    }
    
    /* Hide main content by default */
    #main-content {
      display: none;
    }
    
    /* Show main content when body has loaded class */
    body.loaded #main-content {
      display: block;
    }
    
    /* Hide error by default */
    #error {
      display: none;
    }
    
    /* Show error when body has error class */
    body.error #error {
      display: block;
    }
    
    /* Loading spinner */
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #4e7eff;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      color: #333;
      line-height: 1.6;
    }
    .block-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      max-width: 500px;
      width: 90%;
      text-align: center;
    }
    h1 {
      color: #e74c3c;
      margin-top: 0;
    }
    .site-url {
      font-weight: bold;
      word-break: break-all;
    }
    .task-info {
      background-color: #f8f9fa;
      border-left: 4px solid #4e7eff;
      padding: 1rem;
      margin: 1.5rem 0;
      text-align: left;
      border-radius: 0 4px 4px 0;
    }
    .task-info h3 {
      margin-top: 0;
      color: #2c3e50;
    }
    .buttons {
      margin-top: 1.5rem;
      display: flex;
      justify-content: center;
      gap: 1rem;
      flex-wrap: wrap;
    }
    button {
      background-color: #4e7eff;
      color: white;
      border: none;
      padding: 0.6rem 1.2rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: background-color 0.2s;
      min-width: 120px;
    }
    button.secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
    }
    button:hover {
      background-color: #3b5fd9;
    }
    button.secondary:hover {
      background-color: #e5e5e5;
    }
    .timer {
      margin-top: 1.5rem;
      font-size: 0.9rem;
      color: #666;
    }
    @media (max-width: 480px) {
      .buttons {
        flex-direction: column;
      }
      button {
        width: 100%;
      }
    }
    
    /* Loading spinner */
    .loading-state {
      text-align: center;
      padding: 2rem;
    }
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #4e7eff;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Error state */
    .error-state {
      text-align: center;
      padding: 2rem;
      max-width: 500px;
      margin: 0 auto;
    }
    
    .error-state h2 {
      color: #e74c3c;
      margin-top: 0;
    }
    
    /* Button styles */
    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .btn.primary {
      background-color: #4e7eff;
      color: white;
    }
    
    .btn.secondary {
      background-color: #f0f0f0;
      color: #333;
    }
    
    .btn:hover {
      opacity: 0.9;
    }
    
    /* State handling */
    body.loading #loading { display: block; }
    body.loading #error,
    body.loading #main-content { display: none; }
    
    body.loaded #loading { display: none; }
    body.loaded #main-content { display: block; }
    
    body.error #loading { display: none; }
    body.error #error { display: block; }
    body.error #main-content { display: none; }
  </style>
</head>
<body class="loading">
  <!-- Loading state -->
  <div id="loading" class="loading-state">
    <div class="spinner"></div>
    <p>Loading block page...</p>
  </div>
  
  <!-- Error state (hidden by default) -->
  <div id="error" class="error-state" style="display: none;">
    <h2>Error</h2>
    <p id="error-message">An unknown error occurred.</p>
    <button id="retry-btn" class="btn primary">Retry</button>
  </div>
  
  <!-- Main content (hidden until loaded) -->
  <div id="main-content" class="block-container" style="display: none;">
    <h1>Site Blocked</h1>
    <p>You're trying to access <span id="blocked-url" class="site-url"></span> during a scheduled task time.</p>
    
    <div class="task-info">
      <h3>Current Task: <span id="task-title"></span></h3>
      <p>Focus on completing your task before visiting this site.</p>
    </div>
    
    <div class="buttons">
      <button id="back-btn" class="secondary">Go Back</button>
      <button id="open-task-btn">Open CalenTask</button>
    </div>
    
    <div class="timer">
      <p>Blocked attempts: <span id="blocked-attempts">0</span></p>
    </div>
  </div>

  <!-- Load the main script -->
  <script src="block-page.js" defer></script>
  
  <!-- Fallback script in case the main script fails to load -->
  <script>
    // This script will only run if the main script fails to load
    window.addEventListener('error', function(e) {
      if (e.target.tagName === 'SCRIPT' && !window.blockPageInitialized) {
        console.error('Script loading error:', e);
        // Show fallback UI
        document.body.innerHTML = `
          <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
            <h2>Site Blocked</h2>
            <p>This site is currently blocked by CalenTask.</p>
            <p>Please complete your scheduled task before visiting this site.</p>
            <div style="margin-top: 20px;">
              <button onclick="window.history.back()" style="padding: 8px 16px; margin: 5px; cursor: pointer;">Go Back</button>
              <button onclick="window.close()" style="padding: 8px 16px; margin: 5px; cursor: pointer;">Close</button>
            </div>
          </div>
        `;
      }
    }, true);
    
    // Set a timeout to check if the main script loaded
    setTimeout(function() {
      if (!window.blockPageInitialized) {
        console.error('Block page script failed to initialize');
        document.body.innerHTML = `
          <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
            <h2>Loading Error</h2>
            <p>Unable to load the block page. Please try again later.</p>
            <button onclick="window.location.reload()" style="padding: 8px 16px; margin: 10px; cursor: pointer;">Reload</button>
          </div>
        `;
      }
    }, 2000);
  </script>
</body>
</html>

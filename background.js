// Function to update the badge with the count of active tasks
function updateBadgeCount() {
  chrome.storage.local.get(['tasks'], (result) => {
    const tasks = result.tasks || [];
    const activeTaskCount = tasks.length;
    
    // Set the badge text to the number of active tasks
    if (activeTaskCount > 0) {
      chrome.action.setBadgeText({ text: activeTaskCount.toString() });
      chrome.action.setBadgeBackgroundColor({ color: '#ff0000' }); // Red background
      
      // Note: Badge text color is automatically set to white for contrast in most Chrome versions
      // The setBadgeTextColor API might not be available in all Chrome versions
      if (chrome.action.setBadgeTextColor) {
        chrome.action.setBadgeTextColor({ color: '#ffffff' }); // White text
      }
    } else {
      chrome.action.setBadgeText({ text: '' }); // Clear the badge if no tasks
    }
  });
}

// Listen for clicks on the extension icon
chrome.action.onClicked.addListener((tab) => {
  // Open the task manager in a new tab
  chrome.tabs.create({ url: 'todo.html' });
});

// Listen for changes to the storage
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local' && changes.tasks) {
    updateBadgeCount();
  }
});

// Update badge when the extension is loaded
updateBadgeCount();

// Content script for CalenTask website blocker
console.log('CalenTask content script loaded at:', new Date().toISOString());

// Function to check if the current URL should be blocked
function checkIfShouldBlock() {
  // Get the current URL
  const currentUrl = window.location.href;
  console.log('Checking if URL should be blocked:', currentUrl);
  
  // Prevent checking on extension pages
  if (currentUrl.startsWith('chrome-extension://') || 
      currentUrl.startsWith('chrome://') || 
      currentUrl.startsWith('about:')) {
    console.log('Skipping check for extension/internal page:', currentUrl);
    return;
  }
  
  // Request data from background script
  console.log('Sending message to background script...');
  chrome.runtime.sendMessage(
    { 
      action: 'checkIfShouldBlock', 
      url: currentUrl,
      timestamp: new Date().toISOString()
    },
    (response) => {
      console.log('Received response from background script:', response);
      
      if (chrome.runtime.lastError) {
        console.error('Error communicating with background script:', chrome.runtime.lastError.message);
        return;
      }
      
      if (response && response.shouldBlock) {
        console.log('Blocking URL:', currentUrl);
        
        // Get the block page URL from the response or create one if not provided
        let blockPageUrl;
        
        if (response.blockPageUrl) {
          blockPageUrl = new URL(response.blockPageUrl);
          console.log('Using block page URL from background:', response.blockPageUrl);
        } else {
          blockPageUrl = new URL(chrome.runtime.getURL('block-page.html'));
          console.log('Using default block page URL');
        }
        
        // Add query parameters
        const params = new URLSearchParams();
        params.append('url', currentUrl);
        params.append('taskTitle', response.activeTask?.title || 'Scheduled Task');
        params.append('attempts', response.blockedAttempts || '0');
        params.append('timestamp', response.timestamp || new Date().toISOString());
        
        // Set the search parameters
        blockPageUrl.search = params.toString();
        
        console.log('Final block page URL:', blockPageUrl.toString());
        
        // Only redirect if we're not already on the block page to prevent infinite redirects
        if (!window.location.href.startsWith(chrome.runtime.getURL('block-page.html'))) {
          console.log('Redirecting to block page...');
          // Use replace instead of assign to prevent back button issues
          window.location.replace(blockPageUrl.toString());
        } else {
          console.log('Already on block page, not redirecting');
        }
      } else {
        console.log('No blocking required for URL:', currentUrl);
        if (response && response.debug) {
          console.log('Debug info:', response.debug);
        }
      }
    }
  );
}

// Run the check immediately when the content script loads
console.log('Initial check for blocking...');
checkIfShouldBlock();

// Also listen for history state changes (for SPA navigation)
window.addEventListener('popstate', checkIfShouldBlock);
window.addEventListener('pushState', checkIfShouldBlock);
window.addEventListener('replaceState', checkIfShouldBlock);

console.log('Content script initialization complete');

// Block page script loaded
console.log('Block page script loaded - Starting execution');

// Mark that the script has loaded
window.blockPageInitialized = true;

// Debug: Log all URL parameters
console.log('Current URL:', window.location.href);
const params = new URLSearchParams(window.location.search);
console.log('URL Parameters:');
for (const [key, value] of params.entries()) {
  console.log(`- ${key}: ${value}`);
}

// Set initial loading state
document.documentElement.classList.add('loading');
document.body.className = 'loading';

// Simple error handling
function showError(message) {
  console.error('Error:', message);
  try {
    // Try to show error in the UI
    const errorEl = document.getElementById('error');
    const errorMsgEl = document.getElementById('error-message');
    if (errorEl && errorMsgEl) {
      errorMsgEl.textContent = message || 'An unknown error occurred';
      errorEl.style.display = 'block';
      document.body.className = 'error';
    }
  } catch (e) {
    console.error('Could not display error:', e);
  }
}

// Function to safely get URL parameters with fallback
function getUrlParameter(name) {
  try {
    const urlParams = new URLSearchParams(window.location.search);
    const value = urlParams.get(name);
    return value !== null ? decodeURIComponent(value) : '';
  } catch (error) {
    console.error('Error getting URL parameter:', error);
    return '';
  }
}

// Function to safely update element text content
function safeSetText(elementId, text) {
  try {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = text;
      return true;
    } else {
      console.error(`Element with ID '${elementId}' not found`);
      return false;
    }
  } catch (error) {
    console.error(`Error updating element '${elementId}':`, error);
    return false;
  }
}

// Function to show error state
function showError(message) {
  console.error('Showing error:', message);
  try {
    safeSetText('error-message', message || 'An unknown error occurred');
    document.body.classList.remove('loading', 'loaded');
    document.body.classList.add('error');
    
    // Make sure error element is visible
    const errorEl = document.getElementById('error');
    if (errorEl) {
      errorEl.style.display = 'block';
    }
  } catch (e) {
    console.error('Error in showError:', e);
    // Last resort: show alert if everything else fails
    alert('Error: ' + (message || 'An unknown error occurred'));
  }
}

// Function to initialize the block page
function initializeBlockPage() {
  console.log('Initializing block page...');
  
  try {
    // Show loading state
    document.body.className = 'loading';
    
    // Get data from URL parameters
    const originalUrl = getUrlParameter('url');
    const taskTitle = getUrlParameter('taskTitle') || 'Scheduled Task';
    const blockedAttempts = getUrlParameter('attempts') || '0';
    const timestamp = getUrlParameter('timestamp') || new Date().toISOString();
    
    console.log('URL parameters:', { originalUrl, taskTitle, blockedAttempts, timestamp });
    
    // Validate required parameters
    if (!originalUrl) {
      throw new Error('No URL provided in the query parameters');
    }
    
    // Update the UI with the blocked URL and task info
    safeSetText('blocked-url', originalUrl);
    safeSetText('task-title', taskTitle);
    safeSetText('blocked-attempts', blockedAttempts);
    
    // Set up event listeners
    setupEventListeners();
    
    // Show the main content now that we've processed everything
    document.body.classList.remove('loading', 'error');
    document.body.classList.add('loaded');
    
    console.log('Block page initialized successfully');
    
  } catch (error) {
    console.error('Error initializing block page:', error);
    showError(error.message || 'An error occurred while loading the block page.');
  }
}

// Function to set up event listeners
function setupEventListeners() {
  try {
    const backBtn = document.getElementById('back-btn');
    const openTaskBtn = document.getElementById('open-task-btn');
    const retryBtn = document.getElementById('retry-btn');
    
    if (backBtn) {
      backBtn.addEventListener('click', () => {
        console.log('Back button clicked');
        // Try to go back in history, or close the tab if that's not possible
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.close();
        }
      });
    } else {
      console.warn('Back button not found');
    }
    
    if (openTaskBtn) {
      openTaskBtn.addEventListener('click', () => {
        console.log('Open Task Manager button clicked');
        try {
          // Open the CalenTask extension page
          chrome.runtime.sendMessage({ action: 'openCalenTask' }, () => {
            // Close the block page after opening the task manager
            window.close();
          });
        } catch (e) {
          console.error('Error opening task manager:', e);
          showError('Could not open task manager: ' + e.message);
        }
      });
    } else {
      console.warn('Open Task Manager button not found');
    }
    
    if (retryBtn) {
      retryBtn.addEventListener('click', () => {
        console.log('Retry button clicked');
        // Reload the page to try again
        window.location.reload();
      });
    } else {
      console.warn('Retry button not found');
    }
    
  } catch (error) {
    console.error('Error setting up event listeners:', error);
    throw error; // Re-throw to be caught by the main try-catch
  }
}

// Initialize the block page when the DOM is ready
console.log('DOM readyState:', document.readyState);
console.log('Document body classes:', document.body.className);

// Set a flag to prevent the fallback script from showing an error
window.blockPageInitialized = true;

try {
  console.log('Starting block page initialization');
  console.log('Current body class:', document.body.className);
  
  // Force show the loading state
  console.log('Setting body class to loading');
  document.body.className = 'loading';
  console.log('Body class after setting loading:', document.body.className);
  
  // Debug: Log all elements
  console.log('Checking for required elements...');
  const requiredElements = ['loading', 'error', 'main-content', 'error-message', 'blocked-url', 'task-title', 'blocked-attempts', 'back-btn', 'open-task-btn', 'retry-btn'];
  requiredElements.forEach(id => {
    const el = document.getElementById(id);
    console.log(`Element #${id}:`, el ? 'Found' : 'MISSING', el);
  });
  
  // Debug: Log computed styles for loading state
  const initialLoadingElement = document.getElementById('loading');
  if (initialLoadingElement) {
    const styles = window.getComputedStyle(initialLoadingElement);
    console.log('Initial loading element styles:', {
      display: styles.display,
      visibility: styles.visibility,
      opacity: styles.opacity
    });
  } else {
    console.warn('Initial loading element not found');
  }
  
  // Show loading state initially
  console.log('Setting initial loading state');
  document.body.className = 'loading';
  console.log('Body classes after setting loading:', document.body.className);
  
  // Get URL parameters
  function getUrlParam(name) {
    try {
      const params = new URLSearchParams(window.location.search);
      return params.get(name) || '';
    } catch (e) {
      console.error('Error getting URL parameter:', e);
      return '';
    }
  }
  
  // Update the UI with the blocked URL and task info
  const originalUrl = getUrlParam('url');
  const taskTitle = getUrlParam('taskTitle') || 'Scheduled Task';
  const blockedAttempts = getUrlParam('attempts') || '0';
  
  console.log('URL parameters:', { originalUrl, taskTitle, blockedAttempts });
  
  // Update the DOM elements
  const blockedUrlEl = document.getElementById('blocked-url');
  const taskTitleEl = document.getElementById('task-title');
  const blockedAttemptsEl = document.getElementById('blocked-attempts');
  
  if (blockedUrlEl) blockedUrlEl.textContent = originalUrl || 'this site';
  if (taskTitleEl) taskTitleEl.textContent = taskTitle;
  if (blockedAttemptsEl) blockedAttemptsEl.textContent = blockedAttempts;
  
  // Set up event listeners
  const backBtn = document.getElementById('back-btn');
  if (backBtn) {
    backBtn.addEventListener('click', () => {
      if (window.history.length > 1) {
        window.history.back();
      } else {
        window.close();
      }
    });
  }
  
  const openTaskBtn = document.getElementById('open-task-btn');
  if (openTaskBtn) {
    openTaskBtn.addEventListener('click', () => {
      try {
        chrome.runtime.sendMessage({ action: 'openCalenTask' }, () => {
          window.close();
        });
      } catch (e) {
        console.error('Error opening task manager:', e);
        showError('Could not open task manager');
      }
    });
  }
  
  const retryBtn = document.getElementById('retry-btn');
  if (retryBtn) {
    retryBtn.addEventListener('click', () => {
      window.location.reload();
    });
  }
  
  // Show the main content
  console.log('Initialization complete, showing main content');
  console.log('Body class before showing content:', document.body.className);
  
  // Update classes to show content and hide loading/error states
  document.documentElement.classList.remove('loading');
  document.documentElement.classList.add('loaded');
  document.body.className = 'loaded';
  
  // Debug: Log state after changing classes
  console.log('Body class after showing content:', document.body.className);
  
  // Force show main content and hide loading/error
  const finalLoadingElement = document.getElementById('loading');
  const finalErrorElement = document.getElementById('error');
  const finalMainContentElement = document.getElementById('main-content');
  
  console.log('Setting final element display states:', {
    loading: finalLoadingElement ? 'hiding' : 'not found',
    error: finalErrorElement ? 'hiding' : 'not found',
    mainContent: finalMainContentElement ? 'showing' : 'not found'
  });
  
  // Apply display changes
  if (finalLoadingElement) finalLoadingElement.style.display = 'none';
  if (finalErrorElement) finalErrorElement.style.display = 'none';
  if (finalMainContentElement) finalMainContentElement.style.display = 'block';
  
  // Verify final states
  console.log('Final element states:', {
    loadingDisplay: finalLoadingElement ? window.getComputedStyle(finalLoadingElement).display : 'N/A',
    errorDisplay: finalErrorElement ? window.getComputedStyle(finalErrorElement).display : 'N/A',
    mainContentDisplay: finalMainContentElement ? window.getComputedStyle(finalMainContentElement).display : 'N/A',
    bodyClass: document.body.className,
    htmlClass: document.documentElement.className
  });
  
} catch (error) {
  console.error('Error during block page initialization:', error);
  
  // Last resort error handling
  try {
    // Try to show error in the UI
    const errorEl = document.getElementById('error');
    const errorMsgEl = document.getElementById('error-message');
    
    if (errorEl && errorMsgEl) {
      errorMsgEl.textContent = 'Error: ' + (error.message || 'Unknown error');
      errorEl.style.display = 'block';
      document.body.className = 'error';
    } else {
      // If we can't find the error elements, write directly to the body
      document.body.innerHTML = `
        <div style="padding: 20px; font-family: Arial, sans-serif; color: #d32f2f;">
          <h2>Error Loading Block Page</h2>
          <p>${error.message || 'An unknown error occurred'}</p>
          <p>Please check the console for more details.</p>
          <button onclick="window.location.reload()" style="padding: 8px 16px; margin-top: 10px;">
            Reload Page
          </button>
        </div>
      `;
    }
  } catch (e) {
    console.error('Error in error handler:', e);
    // If all else fails, show a basic error
    document.body.innerHTML = '<div style="padding: 20px; color: red;">A critical error occurred. Please restart the extension.</div>';
  }
  console.error('Error initializing block page:', error);
  showError('An error occurred while loading the block page: ' + (error.message || 'Unknown error'));
}
